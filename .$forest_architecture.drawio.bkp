<mxfile host="app.diagrams.net" modified="2025-09-03T00:00:00.000Z" agent="Augment Agent" version="24.7.17">
  <diagram name="Forest Rights Architecture" id="forest-rights-arch">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1400" pageHeight="850" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- External Inputs Container -->
        <mxCell id="2" value="External Inputs" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="20" y="20" width="300" height="260" as="geometry"/>
        </mxCell>
        <mxCell id="3" value="Actor: GramSabha &amp; Communities" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="2">
          <mxGeometry x="10" y="40" width="280" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="4" value="Actor: District &amp; State Officials" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="2">
          <mxGeometry x="10" y="100" width="280" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="5" value="DataSource: Satellite Imagery Provider" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="2">
          <mxGeometry x="10" y="160" width="280" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="6" value="DataSource: Administrative Records" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="2">
          <mxGeometry x="10" y="205" width="280" height="40" as="geometry"/>
        </mxCell>

        <!-- Client Apps Container -->
        <mxCell id="7" value="ClientApps" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="20" y="300" width="300" height="260" as="geometry"/>
        </mxCell>
        <mxCell id="8" value="WebGIS Portal (Map, Layers, Search)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="7">
          <mxGeometry x="10" y="40" width="280" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="9" value="Dashboards &amp; Reports (KPIs, Exports)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="7">
          <mxGeometry x="10" y="105" width="280" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="10" value="Claim Submission UI (Forms, Uploads)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="7">
          <mxGeometry x="10" y="160" width="280" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="11" value="Auth &amp; Role UI (Login, Roles)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="7">
          <mxGeometry x="10" y="215" width="280" height="35" as="geometry"/>
        </mxCell>

        <!-- API Layer Container -->
        <mxCell id="12" value="APILayer" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="360" y="40" width="250" height="160" as="geometry"/>
        </mxCell>
        <mxCell id="13" value="API Gateway (Routing, Validation, Auth)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="12">
          <mxGeometry x="10" y="40" width="230" height="100" as="geometry"/>
        </mxCell>

        <!-- Application Services Container -->
        <mxCell id="14" value="ApplicationServices" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="640" y="20" width="320" height="300" as="geometry"/>
        </mxCell>
        <mxCell id="15" value="ClaimService (Claims, Evidence, Workflow)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="14">
          <mxGeometry x="10" y="40" width="300" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="16" value="AnalyticsService (KPIs, Aggregations, Exports)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="14">
          <mxGeometry x="10" y="95" width="300" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="17" value="AlertService (Change Alerts, Notifications)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="14">
          <mxGeometry x="10" y="145" width="300" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="18" value="AuthService (AuthZ, Roles, Audit Hooks)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="14">
          <mxGeometry x="10" y="195" width="300" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="19" value="IngestionService (ETL Admin/Reference Data)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="14">
          <mxGeometry x="10" y="240" width="300" height="40" as="geometry"/>
        </mxCell>

        <!-- WebGIS Rendering Container -->
        <mxCell id="20" value="WebGISRendering" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="360" y="240" width="250" height="210" as="geometry"/>
        </mxCell>
        <mxCell id="21" value="WebGIS Services (Features &amp; Styles)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="20">
          <mxGeometry x="10" y="40" width="230" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="22" value="VectorTile Server (Claims, Boundaries)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="20">
          <mxGeometry x="10" y="105" width="230" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="23" value="RasterTile Server (Land Cover, Change)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="20">
          <mxGeometry x="10" y="150" width="230" height="40" as="geometry"/>
        </mxCell>

        <!-- ML & Remote Sensing Container -->
        <mxCell id="24" value="MLAndRemoteSensing" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="1000" y="20" width="340" height="330" as="geometry"/>
        </mxCell>
        <mxCell id="25" value="ImageryFetcher (Optical/SAR, Metadata, Cloud Masks)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="24">
          <mxGeometry x="10" y="40" width="320" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="26" value="Preprocessor (Reproject, Tile, Normalize, Fusion, STAC)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="24">
          <mxGeometry x="10" y="90" width="320" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="27" value="SegmentationModel (Land Cover Classes)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="24">
          <mxGeometry x="10" y="140" width="320" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="28" value="ChangeDetectionModel (Bi-Temporal, Forest Loss)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="24">
          <mxGeometry x="10" y="185" width="320" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="29" value="ParcelScorer (Join Predictions to Claims, Stats, Confidence)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="24">
          <mxGeometry x="10" y="230" width="320" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="30" value="MLPipelineOrchestrator (Schedule, Versioning, Provenance)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="24">
          <mxGeometry x="10" y="275" width="320" height="40" as="geometry"/>
        </mxCell>

        <!-- Data Stores Container -->
        <mxCell id="31" value="DataStores" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="1000" y="380" width="340" height="230" as="geometry"/>
        </mxCell>
        <mxCell id="32" value="OperationalDB (Claims, Users, Workflow, Alerts, KPIs, Audit)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="31">
          <mxGeometry x="10" y="40" width="320" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="33" value="SpatialDataStore (Vectors: Claims, Boundaries, PAs)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="31">
          <mxGeometry x="10" y="90" width="320" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="34" value="RasterCatalog (COGs, Masks, Predictions, STAC)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="31">
          <mxGeometry x="10" y="140" width="320" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="35" value="DocumentRepository (Evidence, Photos, Titles, Forms)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="31">
          <mxGeometry x="10" y="190" width="320" height="40" as="geometry"/>
        </mxCell>

        <!-- Security & Governance Container -->
        <mxCell id="36" value="SecurityAndGovernance" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="360" y="480" width="600" height="160" as="geometry"/>
        </mxCell>
        <mxCell id="37" value="IdentityProvider (Users, Roles, MFA, Tokens)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="36">
          <mxGeometry x="10" y="40" width="280" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="38" value="PolicyEnforcer (Row/Column Security, Scoping, Minimization)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="36">
          <mxGeometry x="300" y="40" width="290" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="39" value="AuditTrail (Immutable Logs, Model Versions, Chain-of-Custody)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="36">
          <mxGeometry x="10" y="95" width="580" height="45" as="geometry"/>
        </mxCell>

        <!-- Outputs Container -->
        <mxCell id="40" value="Outputs" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="640" y="340" width="320" height="190" as="geometry"/>
        </mxCell>
        <mxCell id="41" value="Interactive Maps (Claims, CFR/IFR, Land Cover, Change)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="40">
          <mxGeometry x="10" y="40" width="300" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="42" value="Dashboards &amp; Exports (KPIs, CSV/PDF, Scheduled Reports)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="40">
          <mxGeometry x="10" y="85" width="300" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="43" value="Proactive Alerts (Encroachment/Change, Confidence)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="40">
          <mxGeometry x="10" y="130" width="300" height="40" as="geometry"/>
        </mxCell>

        <!-- Key Connections -->
        <!-- Client to API Gateway -->
        <mxCell id="44" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1" source="8" target="13">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="370" as="sourcePoint"/>
            <mxPoint x="380" y="320" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="45" value="Map Requests" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="44">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="46" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1" source="10" target="13">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="485" as="sourcePoint"/>
            <mxPoint x="380" y="435" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="47" value="Claim Forms" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="46">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>

        <!-- API Gateway to Services -->
        <mxCell id="48" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#b85450;strokeWidth=2;" edge="1" parent="1" source="13" target="15">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="610" y="90" as="sourcePoint"/>
            <mxPoint x="660" y="40" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="49" value="Claims API" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="48">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>

        <!-- External Data Sources -->
        <mxCell id="50" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#b85450;strokeWidth=3;" edge="1" parent="1" source="5" target="25">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="200" as="sourcePoint"/>
            <mxPoint x="1000" y="62" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="51" value="Satellite Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="50">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="52" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#b85450;strokeWidth=3;" edge="1" parent="1" source="6" target="19">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="225" as="sourcePoint"/>
            <mxPoint x="640" y="280" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="53" value="Admin Records" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="52">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>

        <!-- ML to Data Stores -->
        <mxCell id="54" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#6c8ebf;strokeWidth=2;" edge="1" parent="1" source="25" target="34">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1160" y="105" as="sourcePoint"/>
            <mxPoint x="1160" y="380" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="55" value="Imagery Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="54">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>

        <!-- Services to Data Stores -->
        <mxCell id="56" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#9673a6;strokeWidth=2;" edge="1" parent="1" source="15" target="32">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="960" y="85" as="sourcePoint"/>
            <mxPoint x="1000" y="422" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="57" value="Claim Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="56">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
