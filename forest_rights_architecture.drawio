<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-09-03T00:00:00.000Z" agent="Augment Agent" etag="forest-rights-v1" version="24.7.17">
  <diagram name="Forest Rights Architecture" id="forest-rights-arch">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1400" pageHeight="850" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- Container: External Inputs -->
        <mxCell id="ext_inputs" value="External Inputs" style="swimlane;childLayout=stackLayout;horizontal=1;rounded=1;strokeWidth=2;align=center;fontStyle=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="20" y="20" width="300" height="260" as="geometry"/>
        </mxCell>
        <mxCell id="actor_gram" value="Actor: GramSabha &amp; Communities" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="ext_inputs">
          <mxGeometry x="10" y="40" width="280" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="actor_officials" value="Actor: District &amp; State Officials" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="ext_inputs">
          <mxGeometry x="10" y="100" width="280" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="ds_sat" value="DataSource: Satellite Imagery Provider" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="ext_inputs">
          <mxGeometry x="10" y="160" width="280" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="ds_admin" value="DataSource: Administrative Records" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="ext_inputs">
          <mxGeometry x="10" y="205" width="280" height="40" as="geometry"/>
        </mxCell>

        <!-- Container: Client Apps -->
        <mxCell id="client_apps" value="ClientApps" style="swimlane;childLayout=stackLayout;horizontal=1;rounded=1;strokeWidth=2;fontStyle=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="20" y="300" width="300" height="260" as="geometry"/>
        </mxCell>
        <mxCell id="comp_webgis" value="WebGIS Portal (Map, Layers, Search)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="client_apps">
          <mxGeometry x="10" y="40" width="280" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="comp_dash" value="Dashboards &amp; Reports (KPIs, Exports)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="client_apps">
          <mxGeometry x="10" y="105" width="280" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="comp_claimui" value="Claim Submission UI (Forms, Uploads)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="client_apps">
          <mxGeometry x="10" y="160" width="280" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="comp_authui" value="Auth &amp; Role UI (Login, Roles)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="client_apps">
          <mxGeometry x="10" y="215" width="280" height="35" as="geometry"/>
        </mxCell>

        <!-- Container: API Layer -->
        <mxCell id="api_layer" value="APILayer" style="swimlane;childLayout=stackLayout;horizontal=1;rounded=1;strokeWidth=2;fontStyle=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="360" y="40" width="250" height="160" as="geometry"/>
        </mxCell>
        <mxCell id="comp_apigw" value="API Gateway (Routing, Validation, Auth)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="api_layer">
          <mxGeometry x="10" y="40" width="230" height="100" as="geometry"/>
        </mxCell>

        <!-- Container: Application Services -->
        <mxCell id="app_services" value="ApplicationServices" style="swimlane;childLayout=stackLayout;horizontal=1;rounded=1;strokeWidth=2;fontStyle=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="640" y="20" width="320" height="300" as="geometry"/>
        </mxCell>
        <mxCell id="svc_claim" value="ClaimService (Claims, Evidence, Workflow)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="app_services">
          <mxGeometry x="10" y="40" width="300" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="svc_analytics" value="AnalyticsService (KPIs, Aggregations, Exports)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="app_services">
          <mxGeometry x="10" y="95" width="300" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="svc_alerts" value="AlertService (Change Alerts, Notifications)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="app_services">
          <mxGeometry x="10" y="145" width="300" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="svc_auth" value="AuthService (AuthZ, Roles, Audit Hooks)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="app_services">
          <mxGeometry x="10" y="195" width="300" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="svc_ingest" value="IngestionService (ETL Admin/Reference Data)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="app_services">
          <mxGeometry x="10" y="240" width="300" height="40" as="geometry"/>
        </mxCell>

        <!-- Container: WebGIS Rendering -->
        <mxCell id="gis_render" value="WebGISRendering" style="swimlane;childLayout=stackLayout;horizontal=1;rounded=1;strokeWidth=2;fontStyle=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="360" y="240" width="250" height="210" as="geometry"/>
        </mxCell>
        <mxCell id="comp_wms" value="WebGIS Services (Features &amp; Styles)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="gis_render">
          <mxGeometry x="10" y="40" width="230" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="comp_vtile" value="VectorTile Server (Claims, Boundaries)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="gis_render">
          <mxGeometry x="10" y="105" width="230" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="comp_rtile" value="RasterTile Server (Land Cover, Change)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="gis_render">
          <mxGeometry x="10" y="150" width="230" height="40" as="geometry"/>
        </mxCell>

        <!-- Container: ML & Remote Sensing -->
        <mxCell id="ml_rs" value="MLAndRemoteSensing" style="swimlane;childLayout=stackLayout;horizontal=1;rounded=1;strokeWidth=2;fontStyle=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="1000" y="20" width="340" height="330" as="geometry"/>
        </mxCell>
        <mxCell id="comp_fetch" value="ImageryFetcher (Optical/SAR, Metadata, Cloud Masks)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="ml_rs">
          <mxGeometry x="10" y="40" width="320" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="comp_prep" value="Preprocessor (Reproject, Tile, Normalize, Fusion, STAC)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="ml_rs">
          <mxGeometry x="10" y="90" width="320" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="comp_seg" value="SegmentationModel (Land Cover Classes)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="ml_rs">
          <mxGeometry x="10" y="140" width="320" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="comp_cd" value="ChangeDetectionModel (Bi-Temporal, Forest Loss)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="ml_rs">
          <mxGeometry x="10" y="185" width="320" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="comp_parcel" value="ParcelScorer (Join Predictions to Claims, Stats, Confidence)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="ml_rs">
          <mxGeometry x="10" y="230" width="320" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="comp_orch" value="MLPipelineOrchestrator (Schedule, Versioning, Provenance)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="ml_rs">
          <mxGeometry x="10" y="275" width="320" height="40" as="geometry"/>
        </mxCell>

        <!-- Container: Data Stores -->
        <mxCell id="data_stores" value="DataStores" style="swimlane;childLayout=stackLayout;horizontal=1;rounded=1;strokeWidth=2;fontStyle=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="1000" y="380" width="340" height="230" as="geometry"/>
        </mxCell>
        <mxCell id="db_oper" value="OperationalDB (Claims, Users, Workflow, Alerts, KPIs, Audit)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="data_stores">
          <mxGeometry x="10" y="40" width="320" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="db_spatial" value="SpatialDataStore (Vectors: Claims, Boundaries, PAs)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="data_stores">
          <mxGeometry x="10" y="90" width="320" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="db_raster" value="RasterCatalog (COGs, Masks, Predictions, STAC)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="data_stores">
          <mxGeometry x="10" y="140" width="320" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="db_docs" value="DocumentRepository (Evidence, Photos, Titles, Forms)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="data_stores">
          <mxGeometry x="10" y="190" width="320" height="40" as="geometry"/>
        </mxCell>

        <!-- Container: Security & Governance -->
        <mxCell id="sec_gov" value="SecurityAndGovernance" style="swimlane;childLayout=stackLayout;horizontal=1;rounded=1;strokeWidth=2;fontStyle=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="360" y="480" width="600" height="160" as="geometry"/>
        </mxCell>
        <mxCell id="comp_idp" value="IdentityProvider (Users, Roles, MFA, Tokens)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="sec_gov">
          <mxGeometry x="10" y="40" width="280" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="comp_policy" value="PolicyEnforcer (Row/Column Security, Scoping, Minimization)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="sec_gov">
          <mxGeometry x="300" y="40" width="290" height="45" as="geometry"/>
        </mxCell>
        <mxCell id="comp_audit" value="AuditTrail (Immutable Logs, Model Versions, Chain-of-Custody)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="sec_gov">
          <mxGeometry x="10" y="95" width="580" height="45" as="geometry"/>
        </mxCell>

        <!-- Outputs -->
        <mxCell id="outputs" value="Outputs" style="swimlane;childLayout=stackLayout;horizontal=1;rounded=1;strokeWidth=2;fontStyle=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="640" y="340" width="320" height="190" as="geometry"/>
        </mxCell>
        <mxCell id="out_maps" value="Interactive Maps (Claims, CFR/IFR, Land Cover, Change)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="outputs">
          <mxGeometry x="10" y="40" width="300" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="out_dash" value="Dashboards &amp; Exports (KPIs, CSV/PDF, Scheduled Reports)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="outputs">
          <mxGeometry x="10" y="85" width="300" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="out_alerts" value="Proactive Alerts (Encroachment/Change, Confidence)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="outputs">
          <mxGeometry x="10" y="130" width="300" height="40" as="geometry"/>
        </mxCell>

        <!-- Edges (Data/Control Flows) -->
        <!-- Client to API Gateway -->
        <mxCell id="e1" edge="1" source="comp_webgis" target="comp_apigw" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#666666;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Map/Query Requests" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e1">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e2" edge="1" source="comp_claimui" target="comp_apigw" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#666666;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Claim Forms &amp; Uploads" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e2">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e3" edge="1" source="comp_dash" target="comp_apigw" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#666666;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Aggregations/Exports" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e3">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <!-- API Gateway to Services -->
        <mxCell id="e4" edge="1" source="comp_apigw" target="comp_wms" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#d79b00;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Tiles/Features" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e4">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e5" edge="1" source="comp_apigw" target="svc_claim" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#b85450;strokeWidth=2;dashed=1;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Create/Update/Get Claim" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e5">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e6" edge="1" source="comp_apigw" target="svc_analytics" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#b85450;strokeWidth=2;dashed=1;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Dashboards/Aggregates" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e6">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e7" edge="1" source="comp_apigw" target="svc_alerts" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#b85450;strokeWidth=2;dashed=1;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Alerts Fetch/Ack" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e7">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e8" edge="1" source="comp_apigw" target="svc_auth" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#b85450;strokeWidth=2;dashed=1;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Token Verify/Roles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e8">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <!-- Services to Data Stores -->
        <mxCell id="e9" edge="1" source="svc_ingest" target="db_spatial" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#82b366;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Reference Layers/Boundaries" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e9">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e10" edge="1" source="svc_claim" target="db_oper" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#9673a6;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Claim Records/Docs Metadata" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e10">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e11" edge="1" source="svc_analytics" target="db_oper" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#9673a6;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Precomputed KPIs/Cache" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e11">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e12" edge="1" source="svc_alerts" target="db_oper" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#9673a6;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Alerts/Receipts" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e12">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <!-- ML Components to Data Stores -->
        <mxCell id="e13" edge="1" source="comp_fetch" target="db_raster" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#6c8ebf;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Raw Scenes/Metadata" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e13">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e14" edge="1" source="comp_prep" target="db_raster" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#6c8ebf;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Clean Tiles/COGs/Masks" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e14">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e15" edge="1" source="comp_seg" target="db_raster" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#6c8ebf;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Land Cover Predictions" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e15">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e16" edge="1" source="comp_cd" target="db_raster" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#6c8ebf;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Change Masks/Alerts" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e16">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e17" edge="1" source="comp_parcel" target="db_oper" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#6c8ebf;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Parcel Stats/Confidence/Version" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e17">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <!-- WebGIS to Data Stores -->
        <mxCell id="e18" edge="1" source="comp_wms" target="db_spatial" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#9673a6;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Feature Queries/Styles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e18">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e19" edge="1" source="comp_rtile" target="db_raster" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#9673a6;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="COGs/Pyramids" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e19">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <!-- ML Orchestrator -->
        <mxCell id="e20" edge="1" source="comp_orch" target="db_oper" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#6c8ebf;strokeWidth=2;dashed=1;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Run Logs/Metrics/Lineage" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e20">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <!-- Security & Governance -->
        <mxCell id="e21" edge="1" source="comp_idp" target="svc_auth" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#d79b00;strokeWidth=2;dashed=1;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Tokens/Roles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e21">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e22" edge="1" source="comp_policy" target="comp_apigw" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#d79b00;strokeWidth=2;dashed=1;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="AuthZ Decisions" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e22">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e23" edge="1" source="comp_audit" target="db_oper" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#d79b00;strokeWidth=2;dashed=1;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Signed Audit Events" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e23">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <!-- External flows -->
        <mxCell id="e24" edge="1" source="ds_sat" target="comp_fetch" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#b85450;strokeWidth=3;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Scenes/Metadata" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e24">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>

        <mxCell id="e25" edge="1" source="ds_admin" target="svc_ingest" parent="1" style="endArrow=classic;html=1;rounded=1;strokeColor=#b85450;strokeWidth=3;">
          <mxGeometry relative="1" as="geometry"/>
          <mxCell value="Claims/Titles/Verification" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="e25">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
              <mxPoint as="offset"/>
            </mxGeometry>
          </mxCell>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
