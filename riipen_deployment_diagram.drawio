<mxfile host="app.diagrams.net" modified="2025-09-03T00:00:00.000Z" agent="Augment Agent" version="24.7.17">
  <diagram name="Riipen Deployment Diagram" id="deployment-diagram">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <mxCell id="aws_cloud" value="AWS Cloud Infrastructure" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;strokeWidth=3;fontSize=16;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="150" y="80" width="1400" height="1000" as="geometry"/>
        </mxCell>

        <mxCell id="vpc" value="VPC (Virtual Private Cloud) - Secure Network" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;strokeWidth=2;fontSize=14;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="200" y="130" width="1300" height="920" as="geometry"/>
        </mxCell>

        <mxCell id="public_subnet" value="Public Subnet" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;strokeWidth=1;dashed=1;fontSize=12;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="250" y="180" width="1200" height="200" as="geometry"/>
        </mxCell>

        <mxCell id="private_subnet" value="Private Subnet" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;strokeWidth=1;dashed=1;fontSize=12;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="250" y="420" width="1200" height="400" as="geometry"/>
        </mxCell>

        <mxCell id="data_subnet" value="Data Subnet" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;strokeWidth=1;dashed=1;fontSize=12;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="250" y="860" width="1200" height="160" as="geometry"/>
        </mxCell>

        <mxCell id="load_balancer" value="Application Load Balancer&#xa;(ALB)&#xa;&#xa;• SSL Termination&#xa;• Health Checks&#xa;• Auto Scaling" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="750" y="220" width="200" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="web_cluster" value="Web Server Cluster&#xa;(React/Next.js Frontend)&#xa;&#xa;• Student Portal&#xa;• Company Dashboard&#xa;• Admin Interface&#xa;• Auto Scaling Group" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="300" y="470" width="200" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="api_cluster" value="API Server Cluster&#xa;(Node.js/Express Backend)&#xa;&#xa;• REST APIs&#xa;• Authentication&#xa;• Business Logic&#xa;• Auto Scaling Group" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="750" y="470" width="200" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="microservices" value="Microservices&#xa;&#xa;• Project Management&#xa;• User Management&#xa;• Notification Service&#xa;• File Processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1200" y="470" width="200" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="database" value="RDS Database Cluster&#xa;(PostgreSQL)&#xa;&#xa;• Multi-AZ Deployment&#xa;• Read Replicas&#xa;• Automated Backups&#xa;• Encryption at Rest" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="900" width="200" height="100" as="geometry"/>
        </mxCell>

        <mxCell id="redis_cache" value="Redis Cache&#xa;&#xa;• Session Storage&#xa;• API Caching&#xa;• Real-time Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffccbc;strokeColor=#d84315;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="750" y="900" width="150" height="100" as="geometry"/>
        </mxCell>

        <mxCell id="backup_storage" value="S3 Backup Storage&#xa;&#xa;• Database Backups&#xa;• File Storage&#xa;• Static Assets&#xa;• Lifecycle Policies" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc99;strokeColor=#d79b00;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="950" y="900" width="150" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="monitoring" value="CloudWatch &amp; Security&#xa;&#xa;• Application Monitoring&#xa;• Log Aggregation&#xa;• Security Scanning&#xa;• Performance Metrics" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="300" y="650" width="180" height="100" as="geometry"/>
        </mxCell>

        <mxCell id="lms_gateway" value="LMS Integration Gateway&#xa;&#xa;• LTI 1.3 Support&#xa;• Canvas Integration&#xa;• Blackboard Integration&#xa;• Moodle Integration" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#155724;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1200" y="650" width="180" height="100" as="geometry"/>
        </mxCell>

        <mxCell id="cdn" value="CloudFront CDN&#xa;&#xa;• Global Content Delivery&#xa;• Static Asset Caching&#xa;• Edge Locations" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#388e3c;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="300" y="220" width="180" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="waf" value="AWS WAF&#xa;&#xa;• DDoS Protection&#xa;• SQL Injection Prevention&#xa;• Rate Limiting" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1200" y="220" width="180" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="students" value="Students" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#e3f2fd;strokeColor=#1976d2;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="50" y="200" width="40" height="80" as="geometry"/>
        </mxCell>

        <mxCell id="companies" value="Companies" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="50" y="320" width="40" height="80" as="geometry"/>
        </mxCell>

        <mxCell id="educators" value="Educators" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#e8f5e8;strokeColor=#388e3c;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="50" y="440" width="40" height="80" as="geometry"/>
        </mxCell>

        <mxCell id="canvas_lms" value="Canvas LMS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=11;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1600" y="600" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="blackboard_lms" value="Blackboard" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=11;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1600" y="680" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="moodle_lms" value="Moodle" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=11;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1600" y="760" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="email_service" value="Email Service&#xa;(SES)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1150" y="900" width="100" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="file_storage" value="File Storage&#xa;(S3)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1280" y="900" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="conn1" value="HTTPS" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#1976d2;" parent="1" source="students" target="cdn" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="250" as="sourcePoint"/>
            <mxPoint x="150" y="200" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn2" value="HTTPS" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#7b1fa2;" parent="1" source="companies" target="load_balancer" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="350" as="sourcePoint"/>
            <mxPoint x="150" y="300" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn3" value="HTTPS" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#388e3c;" parent="1" source="educators" target="load_balancer" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="450" as="sourcePoint"/>
            <mxPoint x="150" y="400" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn4" value="CDN" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#388e3c;" parent="1" source="cdn" target="load_balancer" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="280" as="sourcePoint"/>
            <mxPoint x="550" y="230" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn5" value="Route Traffic" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#82b366;" parent="1" source="load_balancer" target="web_cluster" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="350" as="sourcePoint"/>
            <mxPoint x="800" y="300" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn6" value="Route Traffic" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#82b366;" parent="1" source="load_balancer" target="api_cluster" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="350" as="sourcePoint"/>
            <mxPoint x="900" y="300" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn7" value="API Calls" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#6c8ebf;" parent="1" source="web_cluster" target="api_cluster" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="520" y="530" as="sourcePoint"/>
            <mxPoint x="570" y="480" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn8" value="Service Calls" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#6c8ebf;" parent="1" source="api_cluster" target="microservices" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="970" y="530" as="sourcePoint"/>
            <mxPoint x="1020" y="480" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn9" value="DB Queries" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#b85450;" parent="1" source="api_cluster" target="database" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="600" as="sourcePoint"/>
            <mxPoint x="900" y="550" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn10" value="Cache" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#d84315;" parent="1" source="api_cluster" target="redis_cache" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="600" as="sourcePoint"/>
            <mxPoint x="900" y="550" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn11" value="Backup" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#d79b00;" parent="1" source="database" target="backup_storage" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="720" y="950" as="sourcePoint"/>
            <mxPoint x="770" y="900" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn12" value="Monitoring" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#9673a6;" parent="1" source="api_cluster" target="monitoring" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="600" as="sourcePoint"/>
            <mxPoint x="800" y="550" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn13" value="LTI 1.3" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#155724;" parent="1" source="lms_gateway" target="canvas_lms" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="650" as="sourcePoint"/>
            <mxPoint x="1450" y="600" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn14" value="LTI 1.3" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#155724;" parent="1" source="lms_gateway" target="blackboard_lms" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="700" as="sourcePoint"/>
            <mxPoint x="1450" y="650" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn15" value="LTI 1.3" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#155724;" parent="1" source="lms_gateway" target="moodle_lms" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="750" as="sourcePoint"/>
            <mxPoint x="1450" y="700" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn16" value="Email" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#c62828;" parent="1" source="microservices" target="email_service" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1300" y="600" as="sourcePoint"/>
            <mxPoint x="1350" y="550" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="conn17" value="Files" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#c62828;" parent="1" source="microservices" target="file_storage" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="600" as="sourcePoint"/>
            <mxPoint x="1450" y="550" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="title" value="Riipen Platform - Complete Architecture &amp; Deployment Diagram" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=20;fontColor=#1976d2;" parent="1" vertex="1">
          <mxGeometry x="500" y="30" width="600" height="40" as="geometry"/>
        </mxCell>

        <mxCell id="subtitle" value="AWS Cloud Infrastructure with Multi-Tier Architecture, Microservices, and LMS Integration" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=0;fontSize=12;fontColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="400" y="1100" width="800" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="legend_title" value="Architecture Components:" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1600" y="150" width="150" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="legend_users" value="👥 Users" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=10;" parent="1" vertex="1">
          <mxGeometry x="1600" y="180" width="80" height="20" as="geometry"/>
        </mxCell>

        <mxCell id="legend_cdn" value="🌐 CDN &amp; Security" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#388e3c;fontSize=9;" parent="1" vertex="1">
          <mxGeometry x="1600" y="210" width="120" height="25" as="geometry"/>
        </mxCell>

        <mxCell id="legend_lb" value="⚖️ Load Balancer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;" parent="1" vertex="1">
          <mxGeometry x="1600" y="245" width="120" height="25" as="geometry"/>
        </mxCell>

        <mxCell id="legend_app" value="💻 Applications" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=9;" parent="1" vertex="1">
          <mxGeometry x="1600" y="280" width="120" height="25" as="geometry"/>
        </mxCell>

        <mxCell id="legend_data" value="🗄️ Data Layer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;" parent="1" vertex="1">
          <mxGeometry x="1600" y="315" width="120" height="25" as="geometry"/>
        </mxCell>

        <mxCell id="legend_cache" value="⚡ Cache" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffccbc;strokeColor=#d84315;fontSize=9;" parent="1" vertex="1">
          <mxGeometry x="1600" y="350" width="120" height="25" as="geometry"/>
        </mxCell>

        <mxCell id="legend_storage" value="📦 Storage" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc99;strokeColor=#d79b00;fontSize=9;" parent="1" vertex="1">
          <mxGeometry x="1600" y="385" width="120" height="25" as="geometry"/>
        </mxCell>

        <mxCell id="legend_monitoring" value="📊 Monitoring" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9;" parent="1" vertex="1">
          <mxGeometry x="1600" y="420" width="120" height="25" as="geometry"/>
        </mxCell>

        <mxCell id="legend_integration" value="🔗 Integration" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#155724;fontSize=9;" parent="1" vertex="1">
          <mxGeometry x="1600" y="455" width="120" height="25" as="geometry"/>
        </mxCell>

        <mxCell id="legend_external" value="🌍 External Systems" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=9;" parent="1" vertex="1">
          <mxGeometry x="1600" y="490" width="120" height="25" as="geometry"/>
        </mxCell>

        <mxCell id="features_title" value="Key Features:" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1600" y="540" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="features_list" value="• Auto Scaling Groups&#xa;• Multi-AZ Deployment&#xa;• SSL/TLS Encryption&#xa;• DDoS Protection&#xa;• Real-time Monitoring&#xa;• Automated Backups&#xa;• LTI 1.3 Integration&#xa;• Microservices Architecture&#xa;• CDN Global Distribution&#xa;• Redis Caching" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontStyle=0;fontSize=10;" parent="1" vertex="1">
          <mxGeometry x="1600" y="570" width="150" height="200" as="geometry"/>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
