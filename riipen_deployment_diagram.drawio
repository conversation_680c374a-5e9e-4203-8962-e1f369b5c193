<mxfile host="app.diagrams.net" modified="2025-09-03T00:00:00.000Z" agent="Augment Agent" version="24.7.17">
  <diagram name="Riipen Deployment Diagram" id="deployment-diagram">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <mxCell id="aws_cloud" value="AWS Cloud" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;strokeWidth=3;fontSize=16;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="200" y="100" width="1200" height="900" as="geometry"/>
        </mxCell>
        
        <mxCell id="vpc" value="VPC (Virtual Private Cloud)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;strokeWidth=2;fontSize=14;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="250" y="150" width="1100" height="800" as="geometry"/>
        </mxCell>
        
        <mxCell id="load_balancer" value="Load Balancer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="700" y="200" width="200" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="web_cluster" value="Web Server Cluster" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="400" y="350" width="200" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="api_cluster" value="API Server Cluster" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1000" y="350" width="200" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="database" value="Database Cluster" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="700" y="550" width="200" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="backup_storage" value="Backup Storage (S3)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc99;strokeColor=#d79b00;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1000" y="700" width="200" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="monitoring" value="Monitoring &amp; Security Tools" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="400" y="700" width="200" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="lms_gateway" value="LMS Integration Gateway" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#155724;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="700" y="800" width="200" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="end_users" value="End Users" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="220" width="40" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="external_lms" value="External LMS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1500" y="800" width="150" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="conn1" value="HTTPS" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#d6b656;" parent="1" source="end_users" target="load_balancer" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="150" y="300" as="sourcePoint"/>
            <mxPoint x="200" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn2" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#82b366;" parent="1" source="load_balancer" target="web_cluster" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="300" as="sourcePoint"/>
            <mxPoint x="800" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn3" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#82b366;" parent="1" source="load_balancer" target="api_cluster" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="300" as="sourcePoint"/>
            <mxPoint x="900" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn4" value="Secure DB Connection" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#6c8ebf;" parent="1" source="web_cluster" target="database" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="550" y="500" as="sourcePoint"/>
            <mxPoint x="600" y="450" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn5" value="Secure DB Connection" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#6c8ebf;" parent="1" source="api_cluster" target="database" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1050" y="500" as="sourcePoint"/>
            <mxPoint x="1000" y="450" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn6" value="Backup/Restore" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#d79b00;" parent="1" source="database" target="backup_storage" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="650" as="sourcePoint"/>
            <mxPoint x="900" y="600" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn7" value="Logs/Alerts" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#9673a6;" parent="1" source="vpc" target="monitoring" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="350" y="650" as="sourcePoint"/>
            <mxPoint x="400" y="600" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn8" value="LTI/API" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#155724;" parent="1" source="lms_gateway" target="external_lms" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="950" y="840" as="sourcePoint"/>
            <mxPoint x="1000" y="790" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="title" value="Riipen Platform Deployment Architecture" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=18;" parent="1" vertex="1">
          <mxGeometry x="600" y="50" width="400" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend_title" value="Legend:" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="50" y="400" width="80" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend_env" value="Environment" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" parent="1" vertex="1">
          <mxGeometry x="50" y="440" width="80" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend_network" value="Network" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" parent="1" vertex="1">
          <mxGeometry x="50" y="480" width="80" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend_service" value="Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" parent="1" vertex="1">
          <mxGeometry x="50" y="520" width="80" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend_app" value="Application" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" parent="1" vertex="1">
          <mxGeometry x="50" y="560" width="80" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend_db" value="Database" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" parent="1" vertex="1">
          <mxGeometry x="50" y="600" width="80" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend_storage" value="Storage" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc99;strokeColor=#d79b00;fontSize=10;" parent="1" vertex="1">
          <mxGeometry x="50" y="640" width="80" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend_integration" value="Integration" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#155724;fontSize=10;" parent="1" vertex="1">
          <mxGeometry x="50" y="680" width="80" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend_external" value="External" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=10;" parent="1" vertex="1">
          <mxGeometry x="50" y="720" width="80" height="30" as="geometry"/>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
