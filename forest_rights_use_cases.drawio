<mxfile host="app.diagrams.net" modified="2025-09-03T00:00:00.000Z" agent="Augment Agent" version="24.7.17">
  <diagram name="Forest Rights Use Cases" id="use-cases-diagram">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- System Boundary -->
        <mxCell id="2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#000000;strokeWidth=2;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="300" y="100" width="1000" height="800" as="geometry"/>
        </mxCell>

        <!-- System Title -->
        <mxCell id="2a" value="Forest Rights Management System" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="650" y="70" width="300" height="30" as="geometry"/>
        </mxCell>

        <!-- Core Use Cases - Claim Management -->
        <mxCell id="3" value="Submit Forest&#xa;Rights Claim" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d1ecf1;strokeColor=#0c5460;" parent="1" vertex="1">
          <mxGeometry x="350" y="150" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="4" value="Upload Supporting&#xa;Documents" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d1ecf1;strokeColor=#0c5460;" parent="1" vertex="1">
          <mxGeometry x="500" y="150" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="5" value="Track Claim&#xa;Status" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d1ecf1;strokeColor=#0c5460;" parent="1" vertex="1">
          <mxGeometry x="650" y="150" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="6" value="Review and&#xa;Verify Claims" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;" parent="1" vertex="1">
          <mxGeometry x="800" y="150" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="7" value="Approve/Reject&#xa;Claims" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;" parent="1" vertex="1">
          <mxGeometry x="950" y="150" width="120" height="60" as="geometry"/>
        </mxCell>

        <!-- Mapping and Visualization -->
        <mxCell id="8" value="View Interactive&#xa;Maps" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#155724;" parent="1" vertex="1">
          <mxGeometry x="350" y="240" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="9" value="Search Land&#xa;Parcels" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#155724;" parent="1" vertex="1">
          <mxGeometry x="500" y="240" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="10" value="View Land Cover&#xa;Analysis" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#155724;" parent="1" vertex="1">
          <mxGeometry x="650" y="240" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="11" value="Monitor Forest&#xa;Changes" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#155724;" parent="1" vertex="1">
          <mxGeometry x="800" y="240" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="12" value="Generate&#xa;Boundary Maps" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#155724;" parent="1" vertex="1">
          <mxGeometry x="950" y="240" width="120" height="60" as="geometry"/>
        </mxCell>

        <!-- Analytics and Reporting -->
        <mxCell id="13" value="Generate&#xa;KPI Reports" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e2e3e5;strokeColor=#383d41;" parent="1" vertex="1">
          <mxGeometry x="350" y="330" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="14" value="Export Data" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e2e3e5;strokeColor=#383d41;" parent="1" vertex="1">
          <mxGeometry x="500" y="330" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="15" value="View&#xa;Dashboards" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e2e3e5;strokeColor=#383d41;" parent="1" vertex="1">
          <mxGeometry x="650" y="330" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="16" value="Schedule&#xa;Reports" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e2e3e5;strokeColor=#383d41;" parent="1" vertex="1">
          <mxGeometry x="800" y="330" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="17" value="Analyze Claim&#xa;Trends" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e2e3e5;strokeColor=#383d41;" parent="1" vertex="1">
          <mxGeometry x="950" y="330" width="120" height="60" as="geometry"/>
        </mxCell>

        <!-- Alerts and Notifications -->
        <mxCell id="18" value="Receive Change&#xa;Alerts" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;" parent="1" vertex="1">
          <mxGeometry x="350" y="420" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="19" value="Configure Alert&#xa;Settings" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;" parent="1" vertex="1">
          <mxGeometry x="500" y="420" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="20" value="Acknowledge&#xa;Alerts" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;" parent="1" vertex="1">
          <mxGeometry x="650" y="420" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="21" value="Investigate&#xa;Encroachment" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;" parent="1" vertex="1">
          <mxGeometry x="800" y="420" width="120" height="60" as="geometry"/>
        </mxCell>

        <!-- Data Management -->
        <mxCell id="22" value="Manage Reference&#xa;Data" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffeaa7;strokeColor=#fdcb6e;" parent="1" vertex="1">
          <mxGeometry x="350" y="510" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="23" value="Import Satellite&#xa;Imagery" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffeaa7;strokeColor=#fdcb6e;" parent="1" vertex="1">
          <mxGeometry x="500" y="510" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="24" value="Process ML&#xa;Predictions" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffeaa7;strokeColor=#fdcb6e;" parent="1" vertex="1">
          <mxGeometry x="650" y="510" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="25" value="Maintain Data&#xa;Quality" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffeaa7;strokeColor=#fdcb6e;" parent="1" vertex="1">
          <mxGeometry x="800" y="510" width="120" height="60" as="geometry"/>
        </mxCell>

        <!-- Security and Administration -->
        <mxCell id="26" value="Manage User&#xa;Accounts" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#8e24aa;" parent="1" vertex="1">
          <mxGeometry x="350" y="600" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="27" value="Configure Access&#xa;Permissions" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#8e24aa;" parent="1" vertex="1">
          <mxGeometry x="500" y="600" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="28" value="Audit System&#xa;Activities" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#8e24aa;" parent="1" vertex="1">
          <mxGeometry x="650" y="600" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="29" value="Authenticate&#xa;Users" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#8e24aa;" parent="1" vertex="1">
          <mxGeometry x="800" y="600" width="120" height="60" as="geometry"/>
        </mxCell>

        <!-- Document Management -->
        <mxCell id="30" value="Search&#xa;Documents" style="ellipse;whiteSpace=wrap;html=1;fillColor=#b3e5fc;strokeColor=#0277bd;" parent="1" vertex="1">
          <mxGeometry x="1100" y="150" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="31" value="Link Documents&#xa;to Claims" style="ellipse;whiteSpace=wrap;html=1;fillColor=#b3e5fc;strokeColor=#0277bd;" parent="1" vertex="1">
          <mxGeometry x="1100" y="240" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="32" value="View Document&#xa;History" style="ellipse;whiteSpace=wrap;html=1;fillColor=#b3e5fc;strokeColor=#0277bd;" parent="1" vertex="1">
          <mxGeometry x="1100" y="330" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="33" value="Manage Document&#xa;Versions" style="ellipse;whiteSpace=wrap;html=1;fillColor=#b3e5fc;strokeColor=#0277bd;" parent="1" vertex="1">
          <mxGeometry x="1100" y="420" width="120" height="60" as="geometry"/>
        </mxCell>

        <!-- Advanced Features -->
        <mxCell id="34" value="Run ML&#xa;Analysis" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffccbc;strokeColor=#d84315;" parent="1" vertex="1">
          <mxGeometry x="950" y="510" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="35" value="Configure ML&#xa;Models" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffccbc;strokeColor=#d84315;" parent="1" vertex="1">
          <mxGeometry x="1100" y="510" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="36" value="Monitor System&#xa;Performance" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#8e24aa;" parent="1" vertex="1">
          <mxGeometry x="950" y="600" width="120" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="37" value="Backup and&#xa;Recovery" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#8e24aa;" parent="1" vertex="1">
          <mxGeometry x="1100" y="600" width="120" height="60" as="geometry"/>
        </mxCell>

        <!-- Actors positioned outside system boundary -->
        <!-- Left side actors -->
        <mxCell id="38" value="GramSabha&#xa;Members" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="150" y="150" width="30" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="39" value="Community&#xa;Representatives" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="150" y="250" width="30" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="40" value="District&#xa;Officials" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="150" y="350" width="30" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="41" value="State&#xa;Officials" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="150" y="450" width="30" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="45" value="Satellite&#xa;Imagery&#xa;Provider" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="150" y="550" width="30" height="60" as="geometry"/>
        </mxCell>

        <!-- Right side actors -->
        <mxCell id="42" value="System&#xa;Administrator" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="1400" y="350" width="30" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="43" value="Data&#xa;Analyst" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="1400" y="250" width="30" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="44" value="ML&#xa;Engineer" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1400" y="450" width="30" height="60" as="geometry"/>
        </mxCell>

        <!-- GramSabha Members Connections -->
        <mxCell id="46" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="38" target="3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="300" as="sourcePoint"/>
            <mxPoint x="150" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="47" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="38" target="4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="300" as="sourcePoint"/>
            <mxPoint x="150" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="48" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="38" target="5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="300" as="sourcePoint"/>
            <mxPoint x="150" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="49" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="38" target="8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="300" as="sourcePoint"/>
            <mxPoint x="150" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="50" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="38" target="9" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="300" as="sourcePoint"/>
            <mxPoint x="150" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="51" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="38" target="30" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="300" as="sourcePoint"/>
            <mxPoint x="150" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <!-- Community Representatives Connections -->
        <mxCell id="52" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="39" target="3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="400" as="sourcePoint"/>
            <mxPoint x="150" y="350" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="53" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="39" target="8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="400" as="sourcePoint"/>
            <mxPoint x="150" y="350" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="54" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="39" target="18" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="400" as="sourcePoint"/>
            <mxPoint x="150" y="350" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="55" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="39" target="15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="400" as="sourcePoint"/>
            <mxPoint x="150" y="350" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <!-- District Officials Connections -->
        <mxCell id="56" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="40" target="6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="550" as="sourcePoint"/>
            <mxPoint x="150" y="500" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="57" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="40" target="7" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="550" as="sourcePoint"/>
            <mxPoint x="150" y="500" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="58" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="40" target="11" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="550" as="sourcePoint"/>
            <mxPoint x="150" y="500" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="59" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="40" target="13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="550" as="sourcePoint"/>
            <mxPoint x="150" y="500" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="60" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="40" target="21" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="550" as="sourcePoint"/>
            <mxPoint x="150" y="500" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <!-- State Officials Connections -->
        <mxCell id="61" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="41" target="7" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="700" as="sourcePoint"/>
            <mxPoint x="150" y="650" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="62" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="41" target="17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="700" as="sourcePoint"/>
            <mxPoint x="150" y="650" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="63" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="41" target="16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="700" as="sourcePoint"/>
            <mxPoint x="150" y="650" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="64" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="41" target="22" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="700" as="sourcePoint"/>
            <mxPoint x="150" y="650" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <!-- Data Analyst Connections -->
        <mxCell id="65" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="43" target="13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="300" as="sourcePoint"/>
            <mxPoint x="1400" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="66" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="43" target="14" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="300" as="sourcePoint"/>
            <mxPoint x="1400" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="67" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="43" target="15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="300" as="sourcePoint"/>
            <mxPoint x="1400" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="68" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="43" target="17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="300" as="sourcePoint"/>
            <mxPoint x="1400" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="69" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="43" target="10" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="300" as="sourcePoint"/>
            <mxPoint x="1400" y="250" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <!-- System Administrator Connections -->
        <mxCell id="70" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="42" target="26" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="450" as="sourcePoint"/>
            <mxPoint x="1400" y="400" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="71" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="42" target="27" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="450" as="sourcePoint"/>
            <mxPoint x="1400" y="400" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="72" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="42" target="28" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="450" as="sourcePoint"/>
            <mxPoint x="1400" y="400" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="73" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="42" target="36" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="450" as="sourcePoint"/>
            <mxPoint x="1400" y="400" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="74" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="42" target="37" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="450" as="sourcePoint"/>
            <mxPoint x="1400" y="400" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="75" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="42" target="25" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="450" as="sourcePoint"/>
            <mxPoint x="1400" y="400" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <!-- ML Engineer Connections -->
        <mxCell id="76" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="44" target="23" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="600" as="sourcePoint"/>
            <mxPoint x="1400" y="550" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="77" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="44" target="24" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="600" as="sourcePoint"/>
            <mxPoint x="1400" y="550" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="78" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="44" target="34" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="600" as="sourcePoint"/>
            <mxPoint x="1400" y="550" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="79" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="44" target="35" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="600" as="sourcePoint"/>
            <mxPoint x="1400" y="550" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <!-- External System Connections -->
        <mxCell id="80" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="45" target="23" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="850" as="sourcePoint"/>
            <mxPoint x="150" y="800" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <!-- Additional shared use cases -->
        <mxCell id="81" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="40" target="31" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="550" as="sourcePoint"/>
            <mxPoint x="150" y="500" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="82" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="40" target="32" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="550" as="sourcePoint"/>
            <mxPoint x="150" y="500" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="83" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="42" target="33" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1450" y="450" as="sourcePoint"/>
            <mxPoint x="1400" y="400" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
