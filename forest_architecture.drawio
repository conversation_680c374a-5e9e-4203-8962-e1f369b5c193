<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.5 Chrome/134.0.6998.205 Electron/35.3.0 Safari/537.36" version="27.0.5">
  <diagram name="Forest Rights Architecture" id="forest-rights-arch">
    <mxGraphModel dx="1303" dy="783" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1400" pageHeight="850" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="External Inputs" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="20" y="20" width="300" height="260" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Actor: GramSabha &amp; Communities" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="2" vertex="1">
          <mxGeometry x="10" y="40" width="280" height="50" as="geometry" />
        </mxCell>
        <mxCell id="4" value="Actor: District &amp; State Officials" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="2" vertex="1">
          <mxGeometry x="10" y="100" width="280" height="50" as="geometry" />
        </mxCell>
        <mxCell id="5" value="DataSource: Satellite Imagery Provider" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="2" vertex="1">
          <mxGeometry x="10" y="160" width="280" height="40" as="geometry" />
        </mxCell>
        <mxCell id="6" value="DataSource: Administrative Records" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="2" vertex="1">
          <mxGeometry x="10" y="205" width="280" height="40" as="geometry" />
        </mxCell>
        <mxCell id="7" value="ClientApps" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="20" y="300" width="300" height="260" as="geometry" />
        </mxCell>
        <mxCell id="8" value="WebGIS Portal (Map, Layers, Search)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="7" vertex="1">
          <mxGeometry x="10" y="40" width="280" height="60" as="geometry" />
        </mxCell>
        <mxCell id="9" value="Dashboards &amp; Reports (KPIs, Exports)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="7" vertex="1">
          <mxGeometry x="10" y="105" width="280" height="50" as="geometry" />
        </mxCell>
        <mxCell id="10" value="Claim Submission UI (Forms, Uploads)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="7" vertex="1">
          <mxGeometry x="10" y="160" width="280" height="50" as="geometry" />
        </mxCell>
        <mxCell id="11" value="Auth &amp; Role UI (Login, Roles)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="7" vertex="1">
          <mxGeometry x="10" y="215" width="280" height="35" as="geometry" />
        </mxCell>
        <mxCell id="12" value="APILayer" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="360" y="40" width="250" height="160" as="geometry" />
        </mxCell>
        <mxCell id="13" value="API Gateway (Routing, Validation, Auth)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="12" vertex="1">
          <mxGeometry x="10" y="40" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="14" value="ApplicationServices" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="640" y="20" width="320" height="300" as="geometry" />
        </mxCell>
        <mxCell id="15" value="ClaimService (Claims, Evidence, Workflow)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="14" vertex="1">
          <mxGeometry x="10" y="40" width="300" height="50" as="geometry" />
        </mxCell>
        <mxCell id="16" value="AnalyticsService (KPIs, Aggregations, Exports)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="14" vertex="1">
          <mxGeometry x="10" y="95" width="300" height="45" as="geometry" />
        </mxCell>
        <mxCell id="17" value="AlertService (Change Alerts, Notifications)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="14" vertex="1">
          <mxGeometry x="10" y="145" width="300" height="45" as="geometry" />
        </mxCell>
        <mxCell id="18" value="AuthService (AuthZ, Roles, Audit Hooks)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="14" vertex="1">
          <mxGeometry x="10" y="195" width="300" height="40" as="geometry" />
        </mxCell>
        <mxCell id="19" value="IngestionService (ETL Admin/Reference Data)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="14" vertex="1">
          <mxGeometry x="10" y="240" width="300" height="40" as="geometry" />
        </mxCell>
        <mxCell id="20" value="WebGISRendering" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="360" y="240" width="250" height="210" as="geometry" />
        </mxCell>
        <mxCell id="21" value="WebGIS Services (Features &amp; Styles)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="20" vertex="1">
          <mxGeometry x="10" y="40" width="230" height="60" as="geometry" />
        </mxCell>
        <mxCell id="22" value="VectorTile Server (Claims, Boundaries)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="20" vertex="1">
          <mxGeometry x="10" y="105" width="230" height="40" as="geometry" />
        </mxCell>
        <mxCell id="23" value="RasterTile Server (Land Cover, Change)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="20" vertex="1">
          <mxGeometry x="10" y="150" width="230" height="40" as="geometry" />
        </mxCell>
        <mxCell id="24" value="MLAndRemoteSensing" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="1000" y="20" width="340" height="330" as="geometry" />
        </mxCell>
        <mxCell id="25" value="ImageryFetcher (Optical/SAR, Metadata, Cloud Masks)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="24" vertex="1">
          <mxGeometry x="10" y="40" width="320" height="45" as="geometry" />
        </mxCell>
        <mxCell id="26" value="Preprocessor (Reproject, Tile, Normalize, Fusion, STAC)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="24" vertex="1">
          <mxGeometry x="10" y="90" width="320" height="45" as="geometry" />
        </mxCell>
        <mxCell id="27" value="SegmentationModel (Land Cover Classes)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="24" vertex="1">
          <mxGeometry x="10" y="140" width="320" height="40" as="geometry" />
        </mxCell>
        <mxCell id="28" value="ChangeDetectionModel (Bi-Temporal, Forest Loss)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="24" vertex="1">
          <mxGeometry x="10" y="185" width="320" height="40" as="geometry" />
        </mxCell>
        <mxCell id="29" value="ParcelScorer (Join Predictions to Claims, Stats, Confidence)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="24" vertex="1">
          <mxGeometry x="10" y="230" width="320" height="40" as="geometry" />
        </mxCell>
        <mxCell id="30" value="MLPipelineOrchestrator (Schedule, Versioning, Provenance)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="24" vertex="1">
          <mxGeometry x="10" y="275" width="320" height="40" as="geometry" />
        </mxCell>
        <mxCell id="31" value="DataStores" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="1000" y="380" width="340" height="230" as="geometry" />
        </mxCell>
        <mxCell id="32" value="OperationalDB (Claims, Users, Workflow, Alerts, KPIs, Audit)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="31" vertex="1">
          <mxGeometry x="10" y="40" width="320" height="45" as="geometry" />
        </mxCell>
        <mxCell id="33" value="SpatialDataStore (Vectors: Claims, Boundaries, PAs)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="31" vertex="1">
          <mxGeometry x="10" y="90" width="320" height="45" as="geometry" />
        </mxCell>
        <mxCell id="34" value="RasterCatalog (COGs, Masks, Predictions, STAC)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="31" vertex="1">
          <mxGeometry x="10" y="140" width="320" height="45" as="geometry" />
        </mxCell>
        <mxCell id="35" value="DocumentRepository (Evidence, Photos, Titles, Forms)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="31" vertex="1">
          <mxGeometry x="10" y="190" width="320" height="40" as="geometry" />
        </mxCell>
        <mxCell id="36" value="SecurityAndGovernance" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="360" y="560" width="600" height="161" as="geometry" />
        </mxCell>
        <mxCell id="37" value="IdentityProvider (Users, Roles, MFA, Tokens)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="36" vertex="1">
          <mxGeometry y="26" width="600" height="45" as="geometry" />
        </mxCell>
        <mxCell id="38" value="PolicyEnforcer (Row/Column Security, Scoping, Minimization)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="36" vertex="1">
          <mxGeometry y="71" width="600" height="45" as="geometry" />
        </mxCell>
        <mxCell id="39" value="AuditTrail (Immutable Logs, Model Versions, Chain-of-Custody)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="36" vertex="1">
          <mxGeometry y="116" width="600" height="45" as="geometry" />
        </mxCell>
        <mxCell id="40" value="Outputs" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="640" y="340" width="320" height="190" as="geometry" />
        </mxCell>
        <mxCell id="41" value="Interactive Maps (Claims, CFR/IFR, Land Cover, Change)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="40" vertex="1">
          <mxGeometry x="10" y="40" width="300" height="40" as="geometry" />
        </mxCell>
        <mxCell id="42" value="Dashboards &amp; Exports (KPIs, CSV/PDF, Scheduled Reports)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="40" vertex="1">
          <mxGeometry x="10" y="85" width="300" height="40" as="geometry" />
        </mxCell>
        <mxCell id="43" value="Proactive Alerts (Encroachment/Change, Confidence)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="40" vertex="1">
          <mxGeometry x="10" y="130" width="300" height="40" as="geometry" />
        </mxCell>
        <mxCell id="44" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#666666;strokeWidth=2;" parent="1" source="8" target="13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="370" as="sourcePoint" />
            <mxPoint x="380" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="45" value="Map Requests" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="44" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="46" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#666666;strokeWidth=2;" parent="1" source="10" target="13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="485" as="sourcePoint" />
            <mxPoint x="380" y="435" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="47" value="Claim Forms" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="46" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="48" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#b85450;strokeWidth=2;" parent="1" source="13" target="15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="610" y="90" as="sourcePoint" />
            <mxPoint x="660" y="40" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="49" value="Claims API" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="48" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="50" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#b85450;strokeWidth=3;" parent="1" source="5" target="25" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="200" as="sourcePoint" />
            <mxPoint x="1000" y="62" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="51" value="Satellite Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="50" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="52" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#b85450;strokeWidth=3;" parent="1" source="6" target="19" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="225" as="sourcePoint" />
            <mxPoint x="640" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="53" value="Admin Records" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="52" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="54" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#6c8ebf;strokeWidth=2;edgeStyle=orthogonalEdgeStyle;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1330" y="77.50000000000011" as="sourcePoint" />
            <mxPoint x="1170" y="515" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1420" y="77" />
              <mxPoint x="1420" y="540" />
              <mxPoint x="1170" y="540" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="55" value="Imagery Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="54" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint x="1" y="-25" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="56" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#9673a6;strokeWidth=2;" parent="1" source="15" target="32" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="960" y="85" as="sourcePoint" />
            <mxPoint x="1000" y="422" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="57" value="Claim Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="56" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- Additional Client App to API Gateway connections -->
        <mxCell id="58" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#666666;strokeWidth=2;" parent="1" source="9" target="13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="430" as="sourcePoint" />
            <mxPoint x="370" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="59" value="Dashboard Requests" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="58" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <mxCell id="60" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#666666;strokeWidth=2;" parent="1" source="11" target="13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="532" as="sourcePoint" />
            <mxPoint x="370" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="61" value="Auth Requests" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="60" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- API Gateway to all Application Services -->
        <mxCell id="62" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#b85450;strokeWidth=2;" parent="1" source="13" target="16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="610" y="90" as="sourcePoint" />
            <mxPoint x="650" y="117" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="63" value="Analytics API" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="62" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <mxCell id="64" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#b85450;strokeWidth=2;" parent="1" source="13" target="17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="610" y="90" as="sourcePoint" />
            <mxPoint x="650" y="167" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="65" value="Alerts API" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="64" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <mxCell id="66" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#b85450;strokeWidth=2;" parent="1" source="13" target="18" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="610" y="90" as="sourcePoint" />
            <mxPoint x="650" y="215" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="67" value="Auth API" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="66" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- API Gateway to WebGIS Rendering -->
        <mxCell id="68" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#d79b00;strokeWidth=2;" parent="1" source="13" target="21" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="485" y="140" as="sourcePoint" />
            <mxPoint x="485" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="69" value="Map Services" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="68" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- WebGIS Rendering to Data Stores -->
        <mxCell id="70" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#9673a6;strokeWidth=2;edgeStyle=orthogonalEdgeStyle;" parent="1" source="21" target="33" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="610" y="310" as="sourcePoint" />
            <mxPoint x="1000" y="512" as="targetPoint" />
            <Array as="points">
              <mxPoint x="800" y="310" />
              <mxPoint x="800" y="512" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="71" value="Vector Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="70" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <mxCell id="72" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#9673a6;strokeWidth=2;edgeStyle=orthogonalEdgeStyle;" parent="1" source="23" target="34" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="610" y="370" as="sourcePoint" />
            <mxPoint x="1000" y="562" as="targetPoint" />
            <Array as="points">
              <mxPoint x="820" y="370" />
              <mxPoint x="820" y="562" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="73" value="Raster Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="72" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- ML Pipeline Internal Connections -->
        <mxCell id="74" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#6c8ebf;strokeWidth=2;" parent="1" source="25" target="26" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1170" y="105" as="sourcePoint" />
            <mxPoint x="1170" y="135" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="75" value="Raw Imagery" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="74" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <mxCell id="76" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#6c8ebf;strokeWidth=2;" parent="1" source="26" target="27" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1170" y="135" as="sourcePoint" />
            <mxPoint x="1170" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="77" value="Preprocessed Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="76" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <mxCell id="78" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#6c8ebf;strokeWidth=2;" parent="1" source="26" target="28" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1170" y="135" as="sourcePoint" />
            <mxPoint x="1170" y="225" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="79" value="Change Detection Input" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="78" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- ML Models to Data Stores -->
        <mxCell id="80" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#6c8ebf;strokeWidth=2;edgeStyle=orthogonalEdgeStyle;" parent="1" source="27" target="34" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1330" y="180" as="sourcePoint" />
            <mxPoint x="1170" y="520" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1400" y="180" />
              <mxPoint x="1400" y="520" />
              <mxPoint x="1170" y="520" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="81" value="Land Cover Predictions" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="80" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <mxCell id="82" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#6c8ebf;strokeWidth=2;edgeStyle=orthogonalEdgeStyle;" parent="1" source="28" target="34" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1330" y="225" as="sourcePoint" />
            <mxPoint x="1170" y="520" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1380" y="225" />
              <mxPoint x="1380" y="500" />
              <mxPoint x="1170" y="500" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="83" value="Change Detection Results" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="82" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- ParcelScorer connections -->
        <mxCell id="84" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#6c8ebf;strokeWidth=2;" parent="1" source="29" target="15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1010" y="270" as="sourcePoint" />
            <mxPoint x="960" y="85" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="85" value="Claim Scoring" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="84" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <mxCell id="86" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#6c8ebf;strokeWidth=2;edgeStyle=orthogonalEdgeStyle;" parent="1" source="29" target="32" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1330" y="270" as="sourcePoint" />
            <mxPoint x="1170" y="420" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1360" y="270" />
              <mxPoint x="1360" y="420" />
              <mxPoint x="1170" y="420" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="87" value="Parcel Statistics" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="86" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- ML Orchestrator connections -->
        <mxCell id="88" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#6c8ebf;strokeWidth=2;dashed=1;edgeStyle=orthogonalEdgeStyle;" parent="1" source="30" target="32" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1330" y="315" as="sourcePoint" />
            <mxPoint x="1170" y="420" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1340" y="315" />
              <mxPoint x="1340" y="400" />
              <mxPoint x="1170" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="89" value="Pipeline Logs" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="88" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- Additional Services to Data Stores -->
        <mxCell id="90" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#9673a6;strokeWidth=2;" parent="1" source="16" target="32" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="960" y="117" as="sourcePoint" />
            <mxPoint x="1000" y="462" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="91" value="Analytics Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="90" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <mxCell id="92" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#9673a6;strokeWidth=2;" parent="1" source="17" target="32" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="960" y="167" as="sourcePoint" />
            <mxPoint x="1000" y="462" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="93" value="Alert Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="92" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <mxCell id="94" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#9673a6;strokeWidth=2;" parent="1" source="19" target="33" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="960" y="280" as="sourcePoint" />
            <mxPoint x="1000" y="512" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="95" value="Reference Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="94" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- ClaimService to DocumentRepository -->
        <mxCell id="96" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#9673a6;strokeWidth=2;edgeStyle=orthogonalEdgeStyle;" parent="1" source="15" target="35" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="960" y="85" as="sourcePoint" />
            <mxPoint x="1000" y="610" as="targetPoint" />
            <Array as="points">
              <mxPoint x="980" y="85" />
              <mxPoint x="980" y="610" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="97" value="Documents" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="96" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- Security & Governance Connections -->
        <mxCell id="98" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#d79b00;strokeWidth=2;dashed=1;" parent="1" source="37" target="18" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="660" y="586" as="sourcePoint" />
            <mxPoint x="800" y="235" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="99" value="Identity Services" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="98" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <mxCell id="100" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#d79b00;strokeWidth=2;dashed=1;" parent="1" source="38" target="13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="660" y="631" as="sourcePoint" />
            <mxPoint x="485" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="101" value="Policy Enforcement" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="100" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <mxCell id="102" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#d79b00;strokeWidth=2;dashed=1;edgeStyle=orthogonalEdgeStyle;" parent="1" source="39" target="32" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="960" y="676" as="sourcePoint" />
            <mxPoint x="1170" y="610" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1170" y="676" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="103" value="Audit Logs" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="102" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- Services to Outputs -->
        <mxCell id="104" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#82b366;strokeWidth=2;" parent="1" source="16" target="42" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="790" y="140" as="sourcePoint" />
            <mxPoint x="790" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="105" value="Dashboard Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="104" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <mxCell id="106" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#82b366;strokeWidth=2;" parent="1" source="17" target="43" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="790" y="190" as="sourcePoint" />
            <mxPoint x="790" y="470" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="107" value="Alert Outputs" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="106" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- WebGIS to Outputs -->
        <mxCell id="108" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#82b366;strokeWidth=2;" parent="1" source="21" target="41" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="610" y="310" as="sourcePoint" />
            <mxPoint x="650" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="109" value="Map Outputs" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="108" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
