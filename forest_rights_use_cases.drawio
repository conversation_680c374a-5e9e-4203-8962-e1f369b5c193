<mxfile host="app.diagrams.net" modified="2025-09-03T00:00:00.000Z" agent="Augment Agent" version="24.7.17">
  <diagram name="Forest Rights Use Cases" id="use-cases-diagram">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- System Boundary -->
        <mxCell id="2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#000000;strokeWidth=2;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="250" y="100" width="800" height="600" as="geometry"/>
        </mxCell>

        <!-- System Title -->
        <mxCell id="2a" value="Forest Rights Management System" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="500" y="70" width="300" height="30" as="geometry"/>
        </mxCell>

        <!-- Group 1: Claim Management -->
        <mxCell id="g1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;strokeWidth=1;dashed=1;opacity=30;" parent="1" vertex="1">
          <mxGeometry x="280" y="130" width="480" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="g1_label" value="Claim Management" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=12;fontColor=#1976d2;" parent="1" vertex="1">
          <mxGeometry x="290" y="135" width="120" height="20" as="geometry"/>
        </mxCell>

        <!-- Group 2: Mapping & Visualization -->
        <mxCell id="g2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#388e3c;strokeWidth=1;dashed=1;opacity=30;" parent="1" vertex="1">
          <mxGeometry x="280" y="230" width="480" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="g2_label" value="Mapping & Visualization" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=12;fontColor=#388e3c;" parent="1" vertex="1">
          <mxGeometry x="290" y="235" width="150" height="20" as="geometry"/>
        </mxCell>

        <!-- Group 3: Reports & Analytics -->
        <mxCell id="g3" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;strokeWidth=1;dashed=1;opacity=30;" parent="1" vertex="1">
          <mxGeometry x="280" y="330" width="480" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="g3_label" value="Reports & Analytics" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=12;fontColor=#7b1fa2;" parent="1" vertex="1">
          <mxGeometry x="290" y="335" width="130" height="20" as="geometry"/>
        </mxCell>

        <!-- Group 4: System Administration -->
        <mxCell id="g4" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;strokeWidth=1;dashed=1;opacity=30;" parent="1" vertex="1">
          <mxGeometry x="280" y="430" width="480" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="g4_label" value="System Administration" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=12;fontColor=#f57c00;" parent="1" vertex="1">
          <mxGeometry x="290" y="435" width="150" height="20" as="geometry"/>
        </mxCell>

        <!-- Group 1: Claim Management Use Cases -->
        <mxCell id="3" value="Submit Claim" style="ellipse;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="300" y="155" width="100" height="50" as="geometry"/>
        </mxCell>

        <mxCell id="4" value="Review Claims" style="ellipse;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="420" y="155" width="100" height="50" as="geometry"/>
        </mxCell>

        <mxCell id="5" value="Approve Claims" style="ellipse;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="540" y="155" width="100" height="50" as="geometry"/>
        </mxCell>

        <mxCell id="6" value="Track Status" style="ellipse;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="660" y="155" width="100" height="50" as="geometry"/>
        </mxCell>

        <!-- Group 2: Mapping & Visualization Use Cases -->
        <mxCell id="7" value="View Maps" style="ellipse;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="300" y="255" width="100" height="50" as="geometry"/>
        </mxCell>

        <mxCell id="8" value="Search Land" style="ellipse;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="420" y="255" width="100" height="50" as="geometry"/>
        </mxCell>

        <mxCell id="9" value="Monitor Changes" style="ellipse;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="540" y="255" width="100" height="50" as="geometry"/>
        </mxCell>

        <mxCell id="10" value="Upload Documents" style="ellipse;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="660" y="255" width="100" height="50" as="geometry"/>
        </mxCell>

        <!-- Group 3: Reports & Analytics Use Cases -->
        <mxCell id="11" value="Generate Reports" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="300" y="355" width="100" height="50" as="geometry"/>
        </mxCell>

        <mxCell id="12" value="View Dashboard" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="420" y="355" width="100" height="50" as="geometry"/>
        </mxCell>

        <mxCell id="13" value="Receive Alerts" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="540" y="355" width="100" height="50" as="geometry"/>
        </mxCell>

        <mxCell id="14" value="Export Data" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="660" y="355" width="100" height="50" as="geometry"/>
        </mxCell>

        <!-- Group 4: System Administration Use Cases -->
        <mxCell id="15" value="Manage Users" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="300" y="455" width="100" height="50" as="geometry"/>
        </mxCell>

        <mxCell id="16" value="Configure System" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="420" y="455" width="100" height="50" as="geometry"/>
        </mxCell>

        <mxCell id="17" value="Process Satellite Data" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="540" y="455" width="100" height="50" as="geometry"/>
        </mxCell>

        <mxCell id="18" value="Run Analysis" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="660" y="455" width="100" height="50" as="geometry"/>
        </mxCell>

        <!-- Simplified Actors -->
        <!-- Left side - Primary Users -->
        <mxCell id="19" value="Community&#xa;Members" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="150" y="200" width="30" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="20" value="Government&#xa;Officials" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="150" y="350" width="30" height="60" as="geometry"/>
        </mxCell>

        <!-- Right side - Technical User -->
        <mxCell id="21" value="System&#xa;Admin" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="1150" y="325" width="30" height="60" as="geometry"/>
        </mxCell>

        <!-- Simplified Connections -->
        <!-- Community Members connections -->
        <mxCell id="24" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="19" target="3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="200" as="sourcePoint"/>
            <mxPoint x="250" y="150" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="25" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="19" target="6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="200" as="sourcePoint"/>
            <mxPoint x="250" y="150" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="26" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="19" target="7" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="200" as="sourcePoint"/>
            <mxPoint x="250" y="150" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="27" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="19" target="8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="200" as="sourcePoint"/>
            <mxPoint x="250" y="150" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="28" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="19" target="10" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="200" as="sourcePoint"/>
            <mxPoint x="250" y="150" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <!-- Government Officials connections -->
        <mxCell id="29" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="20" target="4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="350" as="sourcePoint"/>
            <mxPoint x="250" y="300" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="30" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="20" target="5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="350" as="sourcePoint"/>
            <mxPoint x="250" y="300" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="31" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="20" target="9" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="350" as="sourcePoint"/>
            <mxPoint x="250" y="300" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="32" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="20" target="11" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="350" as="sourcePoint"/>
            <mxPoint x="250" y="300" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="33" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="20" target="13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="350" as="sourcePoint"/>
            <mxPoint x="250" y="300" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <!-- System Admin connections (combines admin, analyst, and satellite functions) -->
        <mxCell id="34" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="21" target="11" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1100" y="325" as="sourcePoint"/>
            <mxPoint x="1050" y="275" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="35" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="21" target="12" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1100" y="325" as="sourcePoint"/>
            <mxPoint x="1050" y="275" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="36" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="21" target="14" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1100" y="325" as="sourcePoint"/>
            <mxPoint x="1050" y="275" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="37" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="21" target="15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1100" y="325" as="sourcePoint"/>
            <mxPoint x="1050" y="275" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="38" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="21" target="16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1100" y="325" as="sourcePoint"/>
            <mxPoint x="1050" y="275" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="39" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="21" target="17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1100" y="325" as="sourcePoint"/>
            <mxPoint x="1050" y="275" as="targetPoint"/>
          </mxGeometry>
        </mxCell>

        <mxCell id="40" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="21" target="18" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1100" y="325" as="sourcePoint"/>
            <mxPoint x="1050" y="275" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
